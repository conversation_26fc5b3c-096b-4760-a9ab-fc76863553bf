import asyncio
import logging
import aiohttp
from typing import Dict, Any, Optional

from core.config import settings
from services.device_monitor import DeviceMonitorService
from services.vh_service import VirtualHereService
from schemas import Device<PERSON>tatus, VHInstanceStatus, SlaveStatusReport

logger = logging.getLogger(__name__)

class ReporterService:
    def __init__(
        self,
        device_monitor: DeviceMonitorService,
        vh_service: VirtualHereService
    ):
        self.device_monitor = device_monitor
        self.vh_service = vh_service
        self.slave_id = settings.SLAVE_ID
        self._report_interval = 10
        self._task: Optional[asyncio.Task] = None
        self._session: Optional[aiohttp.ClientSession] = None

    async def start(self):
        if not self._session:
            self._session = aiohttp.ClientSession()
        if self._task is None:
            self._task = asyncio.create_task(self._report_loop())
            logger.info(f"Reporter service started. Reporting every {self._report_interval} seconds.")

    async def stop(self):
        if self._task:
            self._task.cancel()
            self._task = None
            logger.info("Reporter service stopped.")
        if self._session:
            await self._session.close()
            self._session = None

    async def _report_loop(self):
        while True:
            try:
                await self._send_report()
                await asyncio.sleep(self._report_interval)
            except asyncio.CancelledError:
                logger.info("Report loop cancelled.")
                break
            except Exception as e:
                logger.error(f"Error in report loop: {e}", exc_info=True)
                await asyncio.sleep(self._report_interval)

    async def _send_report(self):
        phys_devices = self.device_monitor.get_devices()
        device_statuses = [
            DeviceStatus(
                vendor_id=dev.get('vendor_id'),
                product_id=dev.get('product_id'),
                manufacturer=dev.get('manufacturer'),
                model=dev.get('model'),
                device_path=dev.get('device_path')
            ) for dev in phys_devices.values()
        ]
        
        # TODO: Re-implement VH instance status reporting using the new VirtualHereService.
        # The old implementation was based on managing multiple processes, which is no longer the case.
        # For now, we report an empty list to ensure stability.
        vh_statuses = []

        report = SlaveStatusReport(
            slave_id=self.slave_id,
            devices=device_statuses,
            vh_instances=vh_statuses
        )
        if not self._session:
            self._session = aiohttp.ClientSession()
        target_url = f"{settings.MAIN_SERVER_URL}/api/v1/status/report"
        headers = {"Authorization": f"Bearer {settings.MAIN_SERVER_API_KEY}"}
        try:
            async with self._session.post(target_url, json=report.model_dump(), headers=headers, timeout=5) as response:
                if response.status == 200:
                    logger.info(f"Successfully reported status to {target_url}.")
                else:
                    logger.error(f"Failed to report status. Status: {response.status}, Body: {await response.text()}")
        except Exception as e:
            logger.error(f"Error reporting status to {target_url}: {e}", exc_info=True)
